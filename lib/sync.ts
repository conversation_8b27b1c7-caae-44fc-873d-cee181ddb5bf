import { patientReq } from '@/lib/ccRequest';
import { getDb, dbSchema } from '@/lib/db';
import type { GetCCPatientType } from '@/lib/types/ccTypes';
import type { TNewContact } from '@/lib/db/schema';
import { eq, and, inArray } from 'drizzle-orm';

/**
 * Parameters for the sync operation
 */
export interface SyncParams {
  userId: string;
  useChangedSince?: boolean;
  page?: number;
}

/**
 * Result of the sync operation
 */
export interface SyncResult {
  success: boolean;
  hasMore: boolean;
  page: number;
  results: {
    totalFetched: number;
    totalAdded: number;
    totalSkipped: number;
  };
  error?: string;
  message?: string;
}

/**
 * Generate a past timestamp in ISO format
 * @param minutes - Number of minutes to subtract from current time
 * @returns ISO timestamp string (e.g., "2022-07-12T12:34:56.000Z")
 */
const getPastTimestamp = (minutes: number): string => {
  const now = new Date();
  const pastTime = new Date(now.getTime() - (minutes * 60 * 1000));
  return pastTime.toISOString();
};

/**
 * Get the preferred phone number from patient data
 * Priority: phoneMobile > phonePersonal > phoneBusiness
 */
function getPreferredPhone(patient: GetCCPatientType): string | null {
  return patient.phoneMobile || patient.phonePersonal || patient.phoneBusiness || null;
}

/**
 * Transform CC patient data to contact format
 */
function transformPatientToContact(patient: GetCCPatientType, userId: string): TNewContact {
  const fullName = `${patient.firstName}${patient.lastName ? ' ' + patient.lastName : ''}`;
  return {
    userId,
    ccId: patient.id.toString(),
    name: fullName,
    phone: getPreferredPhone(patient),
  };
}

/**
 * Get existing ccIds for the user in batch
 */
async function getExistingCcIds(db: Awaited<ReturnType<typeof getDb>>, userId: string, ccIds: string[]): Promise<Set<string>> {
  if (ccIds.length === 0) return new Set();

  const existing = await db
    .select({ ccId: dbSchema.contacts.ccId })
    .from(dbSchema.contacts)
    .where(
      and(
        eq(dbSchema.contacts.userId, userId),
        inArray(dbSchema.contacts.ccId, ccIds)
      )
    );

  return new Set(existing.map(row => row.ccId!));
}

/**
 * Sync contacts from CC API to database
 * This is the core synchronization logic extracted from the API route
 *
 * @param params - Sync parameters including userId, useChangedSince, and page
 * @returns Promise<SyncResult> - Result of the sync operation
 *
 * @example
 * ```typescript
 * import { syncContacts } from '@/lib/sync';
 *
 * // Basic sync for a user
 * const result = await syncContacts({
 *   userId: 'user-123',
 *   page: 1
 * });
 *
 * // Incremental sync (only changes in last 60 minutes)
 * const incrementalResult = await syncContacts({
 *   userId: 'user-123',
 *   useChangedSince: true,
 *   page: 1
 * });
 *
 * if (result.success) {
 *   console.log(`Added ${result.results.totalAdded} contacts`);
 *   console.log(`Skipped ${result.results.totalSkipped} contacts`);
 *   console.log(`Has more pages: ${result.hasMore}`);
 * } else {
 *   console.error(`Sync failed: ${result.message}`);
 * }
 * ```
 */
export async function syncContacts(params: SyncParams): Promise<SyncResult> {
  try {
    const { userId, useChangedSince = false, page = 1 } = params;

    const db = await getDb();
    const perPage = 50;

    // Prepare sync parameters
    const syncParams: {
      page: number;
      perPage: number;
      active: boolean;
      sort?: string;
      changedSince?: string;
    } = {
      page,
      perPage,
      active: true,
    };

    // Set changedSince if requested (last 60 minutes for incremental sync)
    if (useChangedSince) {
      syncParams.changedSince = getPastTimestamp(60);
    } else {
      syncParams.sort = "-createdAt";
    }

    // Fetch single page from CC API
    const patients = await patientReq.all(syncParams);

    // Return empty if no patients
    if (!patients || patients.length === 0) {
      return {
        success: true,
        hasMore: false,
        page,
        results: {
          totalFetched: 0,
          totalAdded: 0,
          totalSkipped: 0,
        },
      };
    }

    let totalAdded = 0;
    let totalSkipped = 0;

    // Get all ccIds from this batch for efficient duplicate checking
    const ccIds = patients.map(p => p.id.toString());
    const existingCcIds = await getExistingCcIds(db, userId, ccIds);

    // Prepare contacts to insert
    const contactsToInsert: TNewContact[] = [];

    // Process each patient
    for (const patient of patients) {
      // Skip patients without required data
      if (!patient.firstName) {
        totalSkipped++;
        continue;
      }

      const phone = getPreferredPhone(patient);

      // Skip patients without phone numbers
      if (!phone) {
        totalSkipped++;
        continue;
      }

      // Check if contact already exists by ccId (batch check)
      if (existingCcIds.has(patient.id.toString())) {
        totalSkipped++;
        continue;
      }

      // Transform contact for insertion
      const newContact = transformPatientToContact(patient, userId);
      contactsToInsert.push(newContact);
    }

    // Batch insert all new contacts
    if (contactsToInsert.length > 0) {
      try {
        await db.insert(dbSchema.contacts).values(contactsToInsert);
        totalAdded = contactsToInsert.length;
      } catch (dbError) {
        console.error('Error inserting contacts:', dbError);
        totalSkipped += contactsToInsert.length;
        totalAdded = 0;
      }
    }

    return {
      success: true,
      hasMore: patients.length === perPage, // If we got full page, there might be more
      page,
      results: {
        totalFetched: patients.length,
        totalAdded,
        totalSkipped,
      },
    };

  } catch (error) {
    console.error('Sync error:', error);
    return {
      success: false,
      hasMore: false,
      page: params.page || 1,
      results: {
        totalFetched: 0,
        totalAdded: 0,
        totalSkipped: 0,
      },
      error: 'Sync failed',
      message: 'An error occurred while syncing contacts. Please try again.',
    };
  }
}
