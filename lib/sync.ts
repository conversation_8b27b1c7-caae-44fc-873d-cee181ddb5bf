import { patientReq } from '@/lib/ccRequest';
import { getDb, dbSchema } from '@/lib/db';
import type { GetCCPatientType } from '@/lib/types/ccTypes';
import type { TNewContact } from '@/lib/db/schema';
import { eq, and, inArray } from 'drizzle-orm';

/**
 * Result of the sync operation
 */
export interface SyncResult {
  success: boolean;
  totalPages: number;
  results: {
    totalFetched: number;
    totalAdded: number;
    totalSkipped: number;
  };
  error?: string;
  message?: string;
}

/**
 * Generate a past timestamp in ISO format
 * @param minutes - Number of minutes to subtract from current time
 * @returns ISO timestamp string (e.g., "2022-07-12T12:34:56.000Z")
 */
const getPastTimestamp = (minutes: number): string => {
  const now = new Date();
  const pastTime = new Date(now.getTime() - (minutes * 60 * 1000));
  return pastTime.toISOString();
};

/**
 * Get the preferred phone number from patient data
 * Priority: phoneMobile > phonePersonal > phoneBusiness
 */
function getPreferredPhone(patient: GetCCPatientType): string | null {
  return patient.phoneMobile || patient.phonePersonal || patient.phoneBusiness || null;
}

/**
 * Transform CC patient data to contact format
 */
function transformPatientToContact(patient: GetCCPatientType, userId: string): TNewContact {
  const fullName = `${patient.firstName}${patient.lastName ? ' ' + patient.lastName : ''}`;
  return {
    userId,
    ccId: patient.id.toString(),
    name: fullName,
    phone: getPreferredPhone(patient),
  };
}

/**
 * Get existing ccIds for the user in batch
 */
async function getExistingCcIds(db: Awaited<ReturnType<typeof getDb>>, userId: string, ccIds: string[]): Promise<Set<string>> {
  if (ccIds.length === 0) return new Set();

  const existing = await db
    .select({ ccId: dbSchema.contacts.ccId })
    .from(dbSchema.contacts)
    .where(
      and(
        eq(dbSchema.contacts.userId, userId),
        inArray(dbSchema.contacts.ccId, ccIds)
      )
    );

  return new Set(existing.map(row => row.ccId!));
}

/**
 * Sync contacts from CC API to database
 * This function automatically handles pagination and fetches all available pages
 * Uses incremental sync by default (only changes in last 60 minutes)
 * Always uses userId = "1"
 *
 * @returns Promise<SyncResult> - Aggregated result from all pages
 *
 * @example
 * ```typescript
 * import { syncContacts } from '@/lib/sync';
 *
 * // Sync all contacts with automatic pagination
 * const result = await syncContacts();
 *
 * if (result.success) {
 *   console.log(`Processed ${result.totalPages} pages`);
 *   console.log(`Added ${result.results.totalAdded} contacts`);
 *   console.log(`Skipped ${result.results.totalSkipped} contacts`);
 *   console.log(`Total fetched ${result.results.totalFetched} contacts`);
 * } else {
 *   console.error(`Sync failed: ${result.message}`);
 * }
 * ```
 */
export async function syncContacts(): Promise<SyncResult> {
  try {
    const userId = "1"; // Always use userId = "1"
    const useChangedSince = true; // Always use incremental sync
    const db = await getDb();
    const perPage = 50;

    // Aggregated results across all pages
    let totalFetched = 0;
    let totalAdded = 0;
    let totalSkipped = 0;
    let currentPage = 1;

    while (true) {
      // Prepare sync parameters for current page
      const syncParams: {
        page: number;
        perPage: number;
        active: boolean;
        sort?: string;
        changedSince?: string;
      } = {
        page: currentPage,
        perPage,
        active: true,
      };

      // Set changedSince for incremental sync (last 60 minutes)
      if (useChangedSince) {
        syncParams.changedSince = getPastTimestamp(60);
      } else {
        syncParams.sort = "-createdAt";
      }

      // Fetch current page from CC API
      const patients = await patientReq.all(syncParams);

      // Break if no patients on this page
      if (!patients || patients.length === 0) {
        break;
      }

      // Update total fetched count
      totalFetched += patients.length;

      // Get all ccIds from this batch for efficient duplicate checking
      const ccIds = patients.map(p => p.id.toString());
      const existingCcIds = await getExistingCcIds(db, userId, ccIds);

      // Prepare contacts to insert for this page
      const contactsToInsert: TNewContact[] = [];

      // Process each patient on this page
      for (const patient of patients) {
        // Skip patients without required data
        if (!patient.firstName) {
          totalSkipped++;
          continue;
        }

        const phone = getPreferredPhone(patient);

        // Skip patients without phone numbers
        if (!phone) {
          totalSkipped++;
          continue;
        }

        // Check if contact already exists by ccId (batch check)
        if (existingCcIds.has(patient.id.toString())) {
          totalSkipped++;
          continue;
        }

        // Transform contact for insertion
        const newContact = transformPatientToContact(patient, userId);
        contactsToInsert.push(newContact);
      }

      // Batch insert all new contacts for this page
      if (contactsToInsert.length > 0) {
        try {
          await db.insert(dbSchema.contacts).values(contactsToInsert);
          totalAdded += contactsToInsert.length;
        } catch (dbError) {
          console.error('Error inserting contacts:', dbError);
          totalSkipped += contactsToInsert.length;
        }
      }

      // Check if we should continue to next page
      if (patients.length < perPage) {
        // If we got less than a full page, we're done
        break;
      }

      currentPage++;

      // Safety break to prevent infinite loops
      if (currentPage > 100) {
        console.warn('Sync stopped at page 100 to prevent infinite loop');
        break;
      }
    }

    return {
      success: true,
      totalPages: currentPage,
      results: {
        totalFetched,
        totalAdded,
        totalSkipped,
      },
    };

  } catch (error) {
    console.error('Sync error:', error);
    return {
      success: false,
      totalPages: 0,
      results: {
        totalFetched: 0,
        totalAdded: 0,
        totalSkipped: 0,
      },
      error: 'Sync failed',
      message: 'An error occurred while syncing contacts. Please try again.',
    };
  }
}
