# Sync Utility Documentation

## Overview

The `syncContacts` utility function provides a standalone way to synchronize contacts from the CliniCore API to the database without making HTTP requests to the API endpoint. This function extracts the core synchronization logic from the `/app/api/sync/route.ts` API route handler.

## Location

- **Utility Function**: `/lib/sync.ts`
- **Original API Route**: `/app/api/sync/route.ts` (now uses the utility function)

## Usage

### Basic Import

```typescript
import { syncContacts, type SyncParams, type SyncResult } from '@/lib/sync';
```

### Function Signature

```typescript
function syncContacts(params: SyncParams): Promise<SyncResult>
```

### Parameters

```typescript
interface SyncParams {
  userId: string;           // Required: User ID to sync contacts for
  useChangedSince?: boolean; // Optional: Use incremental sync (last 60 minutes)
  page?: number;            // Optional: Page number for pagination (default: 1)
}
```

### Return Value

```typescript
interface SyncResult {
  success: boolean;
  hasMore: boolean;
  page: number;
  results: {
    totalFetched: number;
    totalAdded: number;
    totalSkipped: number;
  };
  error?: string;      // Present when success is false
  message?: string;    // Present when success is false
}
```

## Examples

### 1. Basic Sync

```typescript
import { syncContacts } from '@/lib/sync';

async function performBasicSync(userId: string) {
  const result = await syncContacts({
    userId,
    page: 1
  });

  if (result.success) {
    console.log(`Successfully synced page ${result.page}`);
    console.log(`Added: ${result.results.totalAdded} contacts`);
    console.log(`Skipped: ${result.results.totalSkipped} contacts`);
    console.log(`Has more pages: ${result.hasMore}`);
  } else {
    console.error(`Sync failed: ${result.message}`);
  }
}
```

### 2. Incremental Sync

```typescript
async function performIncrementalSync(userId: string) {
  const result = await syncContacts({
    userId,
    useChangedSince: true, // Only sync changes from last 60 minutes
    page: 1
  });

  return result;
}
```

### 3. Multi-page Sync

```typescript
async function performFullSync(userId: string) {
  let page = 1;
  let totalAdded = 0;
  let totalSkipped = 0;
  let totalFetched = 0;

  while (true) {
    const result = await syncContacts({
      userId,
      page,
      useChangedSince: false
    });

    if (!result.success) {
      throw new Error(`Sync failed on page ${page}: ${result.message}`);
    }

    // Accumulate results
    totalAdded += result.results.totalAdded;
    totalSkipped += result.results.totalSkipped;
    totalFetched += result.results.totalFetched;

    console.log(`Page ${page}: +${result.results.totalAdded} contacts`);

    // Break if no more pages
    if (!result.hasMore) {
      break;
    }

    page++;

    // Safety break to prevent infinite loops
    if (page > 100) {
      console.warn('Sync stopped at page 100 to prevent infinite loop');
      break;
    }
  }

  return {
    totalAdded,
    totalSkipped,
    totalFetched,
    totalPages: page
  };
}
```

### 4. Server Action Usage

```typescript
// app/actions/sync.ts
'use server';

import { syncContacts } from '@/lib/sync';
import { initAuth } from '@/lib/auth/config';
import { headers } from 'next/headers';

export async function performSyncAction(useChangedSince: boolean = false) {
  // Get authenticated user
  const auth = await initAuth();
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user?.id) {
    throw new Error('Unauthorized');
  }

  // Perform sync
  const result = await syncContacts({
    userId: session.user.id,
    useChangedSince,
    page: 1
  });

  return result;
}
```

### 5. Background Job Usage

```typescript
// lib/jobs/sync-job.ts
import { syncContacts } from '@/lib/sync';
import { getDb, dbSchema } from '@/lib/db';

export async function syncAllUsers() {
  const db = await getDb();
  
  // Get all users
  const users = await db.select({ id: dbSchema.user.id }).from(dbSchema.user);

  const results = [];

  for (const user of users) {
    try {
      const result = await syncContacts({
        userId: user.id,
        useChangedSince: true, // Incremental sync for background jobs
        page: 1
      });

      results.push({
        userId: user.id,
        success: result.success,
        added: result.results.totalAdded,
        skipped: result.results.totalSkipped
      });

      console.log(`User ${user.id}: +${result.results.totalAdded} contacts`);
    } catch (error) {
      console.error(`Sync failed for user ${user.id}:`, error);
      results.push({
        userId: user.id,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  return results;
}
```

## Benefits

1. **Reusability**: Can be called from server actions, background jobs, or other server-side code
2. **No HTTP Overhead**: Direct function calls instead of HTTP requests
3. **Type Safety**: Full TypeScript support with proper interfaces
4. **Error Handling**: Consistent error handling and return format
5. **Maintainability**: Single source of truth for sync logic
6. **Testing**: Easier to unit test than API endpoints

## Notes

- This function is **server-side only** due to database operations
- For client-side usage, continue using the `/api/sync` endpoint
- The API route now uses this utility function internally
- All business logic and error handling remains the same as the original implementation
