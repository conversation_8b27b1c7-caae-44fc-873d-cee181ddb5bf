import { drizzle } from 'drizzle-orm/neon-serverless';
import { Pool } from '@neondatabase/serverless';
import { serverLogs } from '@/lib/db/schema';
import { eq, desc, and, gte, lte, like, count } from 'drizzle-orm';
import { getConfig } from '@/lib/config';
import { getDb } from '@/lib/db';

export type LogLevel = 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';

/**
 * Create a fresh database connection for logging
 * This prevents I/O sharing issues in Cloudflare Workers
 */
function createLogDb() {
  const config = getConfig();
  const pool = new Pool({
    connectionString: config.DATABASE_URL,
    // Optimize for Workers environment - single connection for logging
    max: 1,
    idleTimeoutMillis: 5000, // 5 seconds
    connectionTimeoutMillis: 3000, // 3 seconds
  });
  return drizzle(pool);
}

export interface LogEntry {
  id?: string;
  userId?: string;
  phonebookKeyPasswordsId?: string;
  timestamp: Date;
  level: LogLevel;
  message: string;
  source: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface LogFilters {
  level?: LogLevel;
  source?: string;
  userId?: string;
  startDate?: Date;
  endDate?: Date;
  search?: string;
}

export interface PaginationOptions {
  page?: number;
  limit?: number;
}

/**
 * Add a log entry to the database
 * GDPR Compliant: Logs are stored with proper data retention considerations
 * Non-blocking: Database insertions are fire-and-forget with fallback to console logging
 *
 * IMPORTANT: This function is designed to be non-blocking. While it returns a Promise,
 * the actual database insertion is wrapped in Promise.resolve() to prevent blocking
 * the main request flow. Logging failures will not interrupt application functionality.
 */
export async function addServerLog(
  level: LogLevel,
  message: string,
  source: string,
  userId?: string,
  phonebookKeyPasswordsId?: string
): Promise<void> {
  // Input validation
  if (!message || !source) {
    console.warn('Invalid log entry: message and source are required');
    return;
  }

  if (!['DEBUG', 'INFO', 'WARN', 'ERROR'].includes(level)) {
    console.warn(`Invalid log level: ${level}`);
    return;
  }

  try {
    // Create fresh database connection for Workers compatibility
    const db = createLogDb();

    // Sanitize inputs to prevent injection attacks and ensure data integrity
    const sanitizedMessage = message
      .replace(/[\x00-\x1F\x7F]/g, '') // Remove control characters
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim()
      .substring(0, 10000);

    const sanitizedSource = source
      .replace(/[\x00-\x1F\x7F]/g, '')
      .replace(/[^a-zA-Z0-9\-_]/g, '') // Only allow safe characters
      .substring(0, 255);

    if (!sanitizedMessage || !sanitizedSource) {
      console.warn('Log entry rejected after sanitization');
      return;
    }

    // Use Promise.resolve to make this non-blocking
    Promise.resolve(
      db.insert(serverLogs).values({
        userId: userId || null,
        phonebookKeyPasswordsId: phonebookKeyPasswordsId || null,
        timestamp: new Date(),
        level,
        message: sanitizedMessage,
        source: sanitizedSource,
      })
    ).catch(error => {
      // Fallback logging to console if database logging fails
      console.error('Failed to write to database log:', error);
      console.log(`[${level}] ${sanitizedSource}: ${sanitizedMessage}`);
    });
  } catch (error) {
    // Immediate fallback for connection errors
    console.error('Database connection failed for logging:', error);
    console.log(`[${level}] ${source}: ${message}`);
  }
}

/**
 * Log HTTP request for GDPR compliance
 * Logs essential request information without sensitive data
 * Optimized: Non-blocking and privacy-focused
 */
export function logHttpRequest(
  method: string,
  url: string,
  userAgent: string,
  ipAddress: string,
  userId?: string,
  statusCode?: number,
  responseTime?: number
): void {
  // Input validation
  if (!method || !url) {
    return;
  }

  try {
    // Remove query parameters that might contain sensitive data
    const sanitizedUrl = url.split('?')[0].substring(0, 200);

    // Privacy-compliant IP masking (GDPR requirement)
    const maskedIp = ipAddress.includes(':')
      ? ipAddress.split(':').slice(0, 4).join(':') + ':xxxx:xxxx:xxxx:xxxx' // IPv6
      : ipAddress.split('.').slice(0, 2).join('.') + '.xxx.xxx'; // IPv4

    // Create GDPR-compliant log message with minimal data
    const messageParts = [
      `${method.toUpperCase()} ${sanitizedUrl}`,
      statusCode ? `${statusCode}` : '',
      responseTime ? `${responseTime}ms` : '',
      `IP: ${maskedIp}`,
      `UA: ${userAgent.substring(0, 50)}${userAgent.length > 50 ? '...' : ''}`
    ].filter(Boolean);

    const message = messageParts.join(' | ');

    // Use non-blocking logging
    addServerLog('INFO', message, 'http-request', userId);
  } catch (error) {
    // Don't let logging errors affect the request
    console.error('HTTP request logging failed:', error);
  }
}

/**
 * Log authentication events for security monitoring
 * Non-blocking: Logging failures will not interrupt authentication flows
 */
export function logAuthEvent(
  event: 'login' | 'logout' | 'failed_login' | 'registration',
  userId?: string,
  details?: string
): void {
  try {
    const message = details ? `${event}: ${details}` : event;
    // Use non-blocking logging - don't await the result
    addServerLog('INFO', message, 'auth', userId).catch(error => {
      // Silent failure for logging errors - log to console but don't throw
      console.error('Failed to log auth event:', error);
      console.log(`[AUTH] ${event}${details ? `: ${details}` : ''} (User: ${userId || 'unknown'})`);
    });
  } catch (error) {
    // Fallback logging to console if there's an immediate error
    console.error('Auth event logging failed:', error);
    console.log(`[AUTH] ${event}${details ? `: ${details}` : ''} (User: ${userId || 'unknown'})`);
  }
}

/**
 * Log API access for phonebook endpoints
 * Non-blocking: Logging failures will not interrupt API responses
 */
export function logApiAccess(
  endpoint: string,
  success: boolean,
  userId?: string,
  phonebookKeyPasswordsId?: string,
  details?: string
): void {
  try {
    const level: LogLevel = success ? 'INFO' : 'WARN';
    const status = success ? 'SUCCESS' : 'FAILED';
    const message = `API ${status}: ${endpoint}${details ? ` - ${details}` : ''}`;

    // Use non-blocking logging - don't await the result
    addServerLog(level, message, 'api', userId, phonebookKeyPasswordsId).catch(error => {
      // Silent failure for logging errors - log to console but don't throw
      console.error('Failed to log API access:', error);
      console.log(`[API] ${status}: ${endpoint}${details ? ` - ${details}` : ''} (User: ${userId || 'unknown'})`);
    });
  } catch (error) {
    // Fallback logging to console if there's an immediate error
    console.error('API access logging failed:', error);
    console.log(`[API] ${success ? 'SUCCESS' : 'FAILED'}: ${endpoint}${details ? ` - ${details}` : ''} (User: ${userId || 'unknown'})`);
  }
}

/**
 * Get server logs with filtering and pagination
 */
export async function getServerLogs(
  filters: LogFilters = {},
  pagination: PaginationOptions = {}
): Promise<{ logs: LogEntry[]; total: number; page: number; totalPages: number }> {
  try {
    // Create fresh database connection for Workers compatibility
    const db = createLogDb();
    const { page = 1, limit = 50 } = pagination;
    const offset = (page - 1) * limit;

    // Build where conditions
    const conditions = [];
    
    if (filters.level) {
      conditions.push(eq(serverLogs.level, filters.level));
    }
    
    if (filters.source) {
      conditions.push(eq(serverLogs.source, filters.source));
    }
    
    if (filters.userId) {
      conditions.push(eq(serverLogs.userId, filters.userId));
    }
    
    if (filters.startDate) {
      conditions.push(gte(serverLogs.timestamp, filters.startDate));
    }
    
    if (filters.endDate) {
      conditions.push(lte(serverLogs.timestamp, filters.endDate));
    }
    
    if (filters.search) {
      conditions.push(like(serverLogs.message, `%${filters.search}%`));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get total count
    const totalResult = await db
      .select({ count: count() })
      .from(serverLogs)
      .where(whereClause);

    const total = totalResult[0]?.count || 0;

    // Get paginated logs
    const logs = await db
      .select()
      .from(serverLogs)
      .where(whereClause)
      .orderBy(desc(serverLogs.timestamp))
      .limit(limit)
      .offset(offset);

    const totalPages = Math.ceil(total / limit);

    return {
      logs: logs.map(log => ({
        id: log.id,
        userId: log.userId || undefined,
        phonebookKeyPasswordsId: log.phonebookKeyPasswordsId || undefined,
        timestamp: log.timestamp,
        level: log.level as LogLevel,
        message: log.message,
        source: log.source,
        createdAt: log.createdAt,
        updatedAt: log.updatedAt,
      })),
      total,
      page,
      totalPages,
    };
  } catch (error) {
    console.error('Failed to fetch server logs:', error);
    return {
      logs: [],
      total: 0,
      page: 1,
      totalPages: 0,
    };
  }
}

/**
 * Clean up old logs for GDPR compliance
 * Should be called periodically to maintain data retention policies
 */
export async function cleanupOldLogs(retentionDays: number = 90): Promise<number> {
  try {
    const db = await getDb();
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    const result = await db
      .delete(serverLogs)
      .where(lte(serverLogs.createdAt, cutoffDate));

    // Non-blocking logging - don't await
    addServerLog('INFO', `Cleaned up logs older than ${retentionDays} days`, 'cleanup').catch(error => {
      console.error('Failed to log cleanup success:', error);
    });

    return result.rowCount || 0;
  } catch (error) {
    console.error('Failed to cleanup old logs:', error);
    // Non-blocking logging - don't await
    addServerLog('ERROR', `Failed to cleanup old logs: ${error}`, 'cleanup').catch(logError => {
      console.error('Failed to log cleanup error:', logError);
    });
    return 0;
  }
}
