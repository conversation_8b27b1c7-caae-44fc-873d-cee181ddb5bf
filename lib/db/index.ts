import { drizzle } from 'drizzle-orm/neon-serverless';
import { Pool } from '@neondatabase/serverless';
import * as schema from './schema';
import { getConfig } from '@/lib/config';

// Database instance and pool cache for performance (development only)
let dbInstance: ReturnType<typeof drizzle> | null = null;
let poolInstance: Pool | null = null;

/**
 * Get database instance with Workers-compatible connection handling
 * For development: uses singleton pattern for performance
 * For production (Workers): creates fresh connection per request to avoid I/O sharing
 */
export async function getDb() {
  const config = getConfig();
  const isDevelopment = config.NEXTJS_ENV === 'development';

  if (!config.DATABASE_URL) {
    throw new Error('DATABASE_URL is required for database connection');
  }

  try {
    if (isDevelopment) {
      // In development, reuse connections for performance
      if (dbInstance && poolInstance) {
        return dbInstance;
      }

      if (!poolInstance) {
        poolInstance = new Pool({
          connectionString: config.DATABASE_URL,
          max: 10, // Maximum number of connections
          idleTimeoutMillis: 30000, // 30 seconds
          connectionTimeoutMillis: 5000, // 5 seconds
        });
      }

      if (!dbInstance) {
        dbInstance = drizzle(poolInstance, {
          schema,
          logger: true
        });
      }

      return dbInstance;
    } else {
      // In production (Workers), create fresh connection per request
      const pool = new Pool({
        connectionString: config.DATABASE_URL,
        max: 1, // Single connection per request
        idleTimeoutMillis: 10000, // 10 seconds
        connectionTimeoutMillis: 5000, // 5 seconds
      });

      return drizzle(pool, {
        schema,
        logger: false
      });
    }
  } catch (error) {
    console.error('Failed to initialize database connection:', error);
    throw new Error(`Database initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Close database connections (useful for cleanup in tests or shutdown)
 */
export async function closeDb(): Promise<void> {
  try {
    if (poolInstance) {
      await poolInstance.end();
      poolInstance = null;
    }
    dbInstance = null;
  } catch (error) {
    console.error('Error closing database connections:', error);
  }
}

// Note: For CLI tools and build-time operations, use getDb() function
// All environment variables are configured in wrangler.jsonc under the "vars" section

// Export schema for use in other files
export * from './schema';
export { dbSchema } from './schema';
