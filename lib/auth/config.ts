import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { nextCookies } from "better-auth/next-js";
import { Pool } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import * as authSchema from '@/lib/db/auth-schema';
import { getConfig } from '@/lib/config';

// Async auth builder function for Cloudflare Workers compatibility
async function authBuilder() {
  const config = getConfig();

  // Create fresh database connection for each auth instance
  // This prevents I/O sharing issues in Cloudflare Workers
  const pool = new Pool({
    connectionString: config.DATABASE_URL,
    // Optimize for Workers environment
    max: 1, // Single connection per request
    idleTimeoutMillis: 10000, // 10 seconds
    connectionTimeoutMillis: 5000, // 5 seconds
  });
  const db = drizzle(pool, { schema: authSchema });

  return betterAuth({
    database: drizzleAdapter(db, {
      provider: "pg",
      schema: authSchema,
    }),
    secret: config.BETTER_AUTH_SECRET,
    baseURL: config.BETTER_AUTH_URL,
    emailAndPassword: {
      enabled: true,
      requireEmailVerification: false,
    },
    socialProviders: {
      google: {
        clientId: config.GOOGLE_CLIENT_ID,
        clientSecret: config.GOOGLE_CLIENT_SECRET,
      },
    },
    session: {
      expiresIn: 60 * 60 * 24 * 7, // 7 days
      updateAge: 60 * 60 * 24, // 1 day
    },
    plugins: [
      nextCookies(), // Must be last plugin for Next.js
    ],
  });
}

// Sync wrapper for backward compatibility (deprecated)
function createAuth() {
  console.warn('createAuth() is deprecated, use authBuilder() instead');
  // This is a temporary sync wrapper - should be replaced with async calls
  const config = getConfig();
  const pool = new Pool({
    connectionString: config.DATABASE_URL,
    max: 1,
    idleTimeoutMillis: 10000,
    connectionTimeoutMillis: 5000,
  });
  const db = drizzle(pool, { schema: authSchema });

  return betterAuth({
    database: drizzleAdapter(db, {
      provider: "pg",
      schema: authSchema,
    }),
    secret: config.BETTER_AUTH_SECRET,
    baseURL: config.BETTER_AUTH_URL,
    emailAndPassword: {
      enabled: true,
      requireEmailVerification: false,
    },
    socialProviders: {
      google: {
        clientId: config.GOOGLE_CLIENT_ID,
        clientSecret: config.GOOGLE_CLIENT_SECRET,
      },
    },
    session: {
      expiresIn: 60 * 60 * 24 * 7, // 7 days
      updateAge: 60 * 60 * 24, // 1 day
    },
    plugins: [
      nextCookies(), // Must be last plugin for Next.js
    ],
  });
}

// Singleton pattern for development, fresh instances for production
let authInstance: Awaited<ReturnType<typeof authBuilder>> | null = null;

// Async initialization function following better-auth-cloudflare patterns
export async function initAuth() {
  const config = getConfig();
  const isDevelopment = config.NEXTJS_ENV === 'development';

  if (isDevelopment) {
    // In development, use singleton to avoid recreating connections
    if (!authInstance) {
      authInstance = await authBuilder();
    }
    return authInstance;
  } else {
    // In production (Cloudflare Workers), always create fresh instance
    // This prevents I/O sharing violations and ensures proper request isolation
    return await authBuilder();
  }
}

// Legacy sync function for backward compatibility (deprecated)
export const getAuth = async () => {
  console.warn('getAuth() is deprecated, use initAuth() instead');
  return await initAuth();
};

// Export a static auth instance for type inference only
// This should NOT be used in runtime code in production
export const auth = createAuth();
