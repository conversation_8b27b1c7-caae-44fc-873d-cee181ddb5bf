import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { initAuth } from "@/lib/auth/config"
import { headers } from "next/headers"
import { Suspense } from "react"
import { SyncButton } from "./components/SyncButton"
import { ContactsTable } from "./components/ContactsTable"

async function ContactsContent() {
  // Get current user session
  const auth = await initAuth();
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user?.id) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            Please log in to view contacts
          </div>
        </CardContent>
      </Card>
    );
  }

  try {

    return (
      <>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Contacts</h1>
            <p className="text-muted-foreground">Manage your business contacts and relationships</p>
          </div>
          <SyncButton showDropdown={true} />
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Contact Directory</CardTitle>
            <CardDescription>
              Your personal contact list for Yealink phone integration
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ContactsTable
              initialContacts={[]}
              initialTotal={0}
            />
          </CardContent>
        </Card>
      </>
    );
  } catch (error) {
    console.error('Error loading contacts:', error);
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-destructive">
            Error loading contacts. Please try again.
          </div>
        </CardContent>
      </Card>
    );
  }
}

export default function ContactsPage() {
  return (
    <div className="space-y-6">
      <Suspense fallback={
        <Card>
          <CardContent className="p-6">
            <div className="text-center">Loading contacts...</div>
          </CardContent>
        </Card>
      }>
        <ContactsContent />
      </Suspense>
    </div>
  );
}
