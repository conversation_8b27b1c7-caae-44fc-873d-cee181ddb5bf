import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Key } from "lucide-react"
import { initAuth } from "@/lib/auth/config"
import { headers } from "next/headers"
import { getUserPhonebookKeys } from "@/lib/phonebook-keys"
import { Suspense } from "react"
import { PhonebookKeysTable } from "./components/phonebook-keys-table"
import { CreateKeyButton } from "./components/create-key-button"

async function PhonebookKeysContent() {
  // Get current user session
  const auth = await initAuth();
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user?.id) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            Please log in to view API keys
          </div>
        </CardContent>
      </Card>
    );
  }

  const userKeys = await getUserPhonebookKeys(session.user.id);

  return (
    <>
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">API Keys</h1>
          <p className="text-muted-foreground">Manage your phonebook API keys for external integrations</p>
        </div>
        <CreateKeyButton className="w-full sm:w-auto" />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Phonebook API Keys</CardTitle>
          <CardDescription>
            API keys allow external applications to access your phonebook data securely
          </CardDescription>
        </CardHeader>
        <CardContent>
          {userKeys.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <div className="mb-4">
                <Key className="w-12 h-12 mx-auto text-muted-foreground/50" />
              </div>
              <h3 className="text-lg font-medium mb-2">No API keys yet</h3>
              <p className="text-sm mb-4">Create your first API key to enable external access to your phonebook</p>
              <CreateKeyButton>
                Create Your First API Key
              </CreateKeyButton>
            </div>
          ) : (
            <PhonebookKeysTable keys={userKeys} />
          )}
        </CardContent>
      </Card>
    </>
  );
}

export default function PhonebookKeysPage() {
  return (
    <div className="space-y-6">
      <Suspense fallback={
        <Card>
          <CardContent className="p-6">
            <div className="text-center">Loading API keys...</div>
          </CardContent>
        </Card>
      }>
        <PhonebookKeysContent />
      </Suspense>
    </div>
  );
}
