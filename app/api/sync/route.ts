import { getDb, dbSchema } from '@/lib/db';
import { initAuth } from '@/lib/auth/config';
import { NextRequest, NextResponse } from 'next/server';
import { eq, count } from 'drizzle-orm';
import { headers } from 'next/headers';
import { syncContacts } from '@/lib/sync';

/**
 * Generate a past timestamp in ISO format
 * @param minutes - Number of minutes to subtract from current time
 * @returns ISO timestamp string (e.g., "2022-07-12T12:34:56.000Z")
 */
const getPastTimestamp = (minutes: number): string => {
  const now = new Date();
  const pastTime = new Date(now.getTime() - (minutes * 60 * 1000));
  return pastTime.toISOString();
};

/**
 * Get the preferred phone number from patient data
 * Priority: phoneMobile > phonePersonal > phoneBusiness
 */
function getPreferredPhone(patient: GetCCPatientType): string | null {
  return patient.phoneMobile || patient.phonePersonal || patient.phoneBusiness || null;
}

/**
 * Transform CC patient data to contact format
 */
function transformPatientToContact(patient: GetCCPatientType, userId: string): TNewContact {
  const fullName = `${patient.firstName}${patient.lastName ? ' ' + patient.lastName : ''}`;
  return {
    userId,
    ccId: patient.id.toString(),
    name: fullName,
    phone: getPreferredPhone(patient),
  };
}

/**
 * Get existing ccIds for the user in batch
 */
async function getExistingCcIds(db: Awaited<ReturnType<typeof getDb>>, userId: string, ccIds: string[]): Promise<Set<string>> {
  if (ccIds.length === 0) return new Set();

  const existing = await db
    .select({ ccId: dbSchema.contacts.ccId })
    .from(dbSchema.contacts)
    .where(
      and(
        eq(dbSchema.contacts.userId, userId),
        inArray(dbSchema.contacts.ccId, ccIds)
      )
    );

  return new Set(existing.map(row => row.ccId!));
}

/**
 * Sync contacts from CC API to database
 * POST /api/sync
 */
export async function POST(request: NextRequest) {
  try {
    // Authentication
    const auth = await initAuth();
    const requestHeaders = request.headers;
    const session = await auth.api.getSession({
      headers: requestHeaders,
    });

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const user = {
      ...session.user,
      role: (session.user as { role?: string }).role || 'user',
    };

    const body = await request.json() as { useChangedSince?: boolean; page?: number };
    const useChangedSince = body.useChangedSince === true;
    const page = body.page || 1;

    const db = await getDb();
    const perPage = 50;

    // Prepare sync parameters
    const syncParams: {
      page: number;
      perPage: number;
      active: boolean;
      sort?: string;
      changedSince?: string;
    } = {
      page,
      perPage,
      active: true,
    };

    // Set changedSince if requested (last 60 minutes for incremental sync)
    if (useChangedSince) {
      syncParams.changedSince = getPastTimestamp(60);
    } else {
      syncParams.sort = "-createdAt";
    }

    // Fetch single page from CC API
    const patients = await patientReq.all(syncParams);

    // Return empty if no patients
    if (!patients || patients.length === 0) {
      return NextResponse.json({
        success: true,
        hasMore: false,
        page,
        results: {
          totalFetched: 0,
          totalAdded: 0,
          totalSkipped: 0,
        },
      });
    }

    let totalAdded = 0;
    let totalSkipped = 0;

    // Get all ccIds from this batch for efficient duplicate checking
    const ccIds = patients.map(p => p.id.toString());
    const existingCcIds = await getExistingCcIds(db, user.id, ccIds);

    // Prepare contacts to insert
    const contactsToInsert: TNewContact[] = [];

    // Process each patient
    for (const patient of patients) {
      // Skip patients without required data
      if (!patient.firstName) {
        totalSkipped++;
        continue;
      }

      const phone = getPreferredPhone(patient);

      // Skip patients without phone numbers
      if (!phone) {
        totalSkipped++;
        continue;
      }

      // Check if contact already exists by ccId (batch check)
      if (existingCcIds.has(patient.id.toString())) {
        totalSkipped++;
        continue;
      }

      // Transform contact for insertion
      const newContact = transformPatientToContact(patient, user.id);
      contactsToInsert.push(newContact);
    }

    // Batch insert all new contacts
    if (contactsToInsert.length > 0) {
      try {
        await db.insert(dbSchema.contacts).values(contactsToInsert);
        totalAdded = contactsToInsert.length;
      } catch (dbError) {
        console.error('Error inserting contacts:', dbError);
        totalSkipped += contactsToInsert.length;
        totalAdded = 0;
      }
    }

    return NextResponse.json({
      success: true,
      hasMore: patients.length === perPage, // If we got full page, there might be more
      page,
      results: {
        totalFetched: patients.length,
        totalAdded,
        totalSkipped,
      },
    });

  } catch (error) {
    console.error('Sync API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Sync failed',
        message: 'An error occurred while syncing contacts. Please try again.',
      },
      { status: 500 }
    );
  }
}

// Keep GET for backward compatibility (returns sync status)
export async function GET() {
  try {
    // Authentication
    const auth = await initAuth();
    const requestHeaders = await headers();
    const session = await auth.api.getSession({
      headers: requestHeaders,
    });

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const user = {
      ...session.user,
      role: (session.user as { role?: string }).role || 'user',
    };

    const db = await getDb();

    // Get user's contact count
    const contactCountResult = await db
      .select({ count: count() })
      .from(dbSchema.contacts)
      .where(eq(dbSchema.contacts.userId, user.id));

    return NextResponse.json({
      status: 'ready',
      userContactCount: contactCountResult[0]?.count || 0,
      message: 'Sync endpoint is ready. Use POST to start sync.',
    });
  } catch (error) {
    console.error('Sync status error:', error);
    return NextResponse.json(
      { error: 'Failed to get sync status' },
      { status: 500 }
    );
  }
}