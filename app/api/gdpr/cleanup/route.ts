import { NextResponse } from 'next/server';
import { runGdprCompliance, DEFAULT_RETENTION_POLICY } from '@/lib/gdpr-compliance';
import { addServerLog } from '@/lib/logging';

/**
 * GDPR Compliance Cleanup API
 * 
 * This endpoint should be called periodically (e.g., daily via cron job)
 * to maintain GDPR compliance by cleaning up old data according to retention policies.
 * 
 * Security: This endpoint should be protected with a secret token in production
 */

export async function POST() {
  try {
    // In production, you should protect this endpoint with a secret token
    // const authHeader = request.headers.get('authorization');
    // const expectedToken = process.env.GDPR_CLEANUP_TOKEN;
    // 
    // if (!authHeader || authHeader !== `Bearer ${expectedToken}`) {
    //   return NextResponse.json(
    //     { error: 'Unauthorized' },
    //     { status: 401 }
    //   );
    // }

    // Non-blocking logging - don't await
    addServerLog(
      'INFO',
      'GDPR compliance cleanup initiated via API',
      'gdpr-api'
    ).catch(error => {
      console.error('Failed to log GDPR cleanup initiation:', error);
    });

    const result = await runGdprCompliance(DEFAULT_RETENTION_POLICY);

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'GDPR compliance cleanup completed successfully',
        details: {
          logsDeleted: result.logsDeleted,
          logsAnonymized: result.logsAnonymized,
          retentionPolicy: DEFAULT_RETENTION_POLICY,
        },
      });
    } else {
      return NextResponse.json({
        success: false,
        message: 'GDPR compliance cleanup failed',
        details: {
          logsDeleted: result.logsDeleted,
          logsAnonymized: result.logsAnonymized,
        },
      }, { status: 500 });
    }
  } catch (error) {
    console.error('GDPR cleanup API error:', error);

    // Non-blocking logging - don't await
    addServerLog(
      'ERROR',
      `GDPR cleanup API failed: ${error}`,
      'gdpr-api'
    ).catch(logError => {
      console.error('Failed to log GDPR cleanup error:', logError);
    });

    return NextResponse.json({
      success: false,
      message: 'Internal server error during GDPR cleanup',
      error: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}

/**
 * GET /api/gdpr/cleanup
 * Get information about the current GDPR retention policy
 */
export async function GET() {
  try {
    return NextResponse.json({
      retentionPolicy: DEFAULT_RETENTION_POLICY,
      description: {
        serverLogs: 'Server logs are retained for security and audit purposes',
        userSessions: 'User session data for authentication tracking',
        apiAccessLogs: 'API access logs for security monitoring',
        userInactivity: 'Period after which user accounts are considered inactive',
      },
      gdprCompliance: {
        dataMinimization: 'We only collect and store necessary data',
        purposeLimitation: 'Data is used only for specified purposes',
        storageMinimization: 'Data is deleted according to retention policies',
        transparency: 'Users can export their data at any time',
        userRights: [
          'Right to access',
          'Right to rectification',
          'Right to erasure',
          'Right to data portability',
          'Right to restrict processing',
        ],
      },
    });
  } catch (error) {
    console.error('Error getting GDPR policy:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// Only allow POST and GET methods
export async function PUT() {
  return NextResponse.json(
    { error: 'Method Not Allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method Not Allowed' },
    { status: 405 }
  );
}
