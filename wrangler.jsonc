/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "phonebook",
  "main": ".open-next/worker.js",
  "compatibility_date": "2025-03-01",
  "compatibility_flags": [
    "nodejs_compat",
    "nodejs_compat_populate_process_env",
    "global_fetch_strictly_public"
  ],
  "assets": {
    "binding": "ASSETS",
    "directory": ".open-next/assets"
  },
  "observability": {
    "enabled": true
  },
  "vars": {
    "NEXTJS_ENV": "production",
    "BETTER_AUTH_URL": "https://phonebook.aincoder.workers.dev",
    "GOOGLE_CLIENT_ID": "587567917765-qnd93vdv4oultgqgmmsm9t7ob1sduedt.apps.googleusercontent.com",
    "EMAIL_FROM": "noreply@localhost",
    "EMAIL_REPLY_TO": "support@localhost"
  },
}
