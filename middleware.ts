import { NextRequest, NextResponse } from "next/server";
import { getSessionCookie } from "better-auth/cookies";
import { logHttpRequest } from "@/lib/logging";

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const startTime = Date.now();

  // Skip middleware for static files and specific API routes
  if (
    pathname.startsWith("/_next") ||
    pathname.startsWith("/favicon.ico") ||
    pathname.startsWith("/api/yealink/directory") // Allow Yealink directory access
  ) {
    return NextResponse.next();
  }

  // Log request function - only logs once per request
  const logRequest = (response: NextResponse, userId?: string): void => {
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    const userAgent = request.headers.get("user-agent") || "Unknown";
    const ipAddress =
      request.headers.get("x-forwarded-for") ||
      request.headers.get("x-real-ip") ||
      "Unknown";

    // Only log meaningful requests (not redirects to prevent spam)
    // Use setTimeout to make logging truly non-blocking in Workers
    if (response.status < 300 || response.status >= 400) {
      setTimeout(() => {
        logHttpRequest(
          request.method,
          pathname,
          userAgent,
          ipAddress,
          userId,
          response.status,
          responseTime
        );
      }, 0);
    }
  };

  // Check for session cookie (fast check, no API call)
  const sessionCookie = getSessionCookie(request);

  // Handle auth API routes - allow them through but log
  if (pathname.startsWith("/api/auth")) {
    const response = NextResponse.next();
    logRequest(response);
    return response;
  }

  // Handle other API routes - return JSON 401 if not authenticated
  if (pathname.startsWith("/api/") && !pathname.includes('/api/yealink/directory')) {
    if (!sessionCookie) {
      const response = NextResponse.json(
        { error: "Unauthorized", message: "Authentication required" },
        { status: 401 }
      );
      logRequest(response);
      return response;
    }
    // API route with valid session - allow through
    const response = NextResponse.next();
    logRequest(response);
    return response;
  }

  // Redirect authenticated users away from auth pages
  if (
    sessionCookie &&
    (pathname.startsWith("/login") ||
      pathname.startsWith("/register") ||
      pathname.startsWith("/forgot-password") ||
      pathname.startsWith("/reset-password"))
  ) {
    const response = NextResponse.redirect(new URL("/dashboard", request.url));
    // Don't log redirects to prevent spam
    return response;
  }

  // Redirect unauthenticated users to login for protected page routes
  if (
    !sessionCookie &&
    (pathname.startsWith("/dashboard") ||
      (pathname !== "/" &&
        !pathname.startsWith("/login") &&
        !pathname.startsWith("/register") &&
        !pathname.startsWith("/forgot-password") &&
        !pathname.startsWith("/reset-password")))
  ) {
    const response = NextResponse.redirect(new URL("/login", request.url));
    // Don't log redirects to prevent spam
    return response;
  }

  // Allow access and log successful requests
  const response = NextResponse.next();
  logRequest(response);
  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Note: We include API routes for proper authentication handling
     */
    "/((?!_next/static|_next/image|favicon.ico).*)",
  ],
};
